@baseUrl = https://localhost:7001
@token = your_jwt_token_here

### Test Değişkenleri
@folderId = 00000000-0000-0000-0000-000000000000
@fileId = 00000000-0000-0000-0000-000000000000

### Folder Rename Test
PUT {{baseUrl}}/api/v1/general/folders/{{folderId}}/rename
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "Name": "Yeniden Adlandırılmış Klasör"
}

### File Rename Test
PUT {{baseUrl}}/api/v1/general/files/rename
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "Id": "{{fileId}}",
    "FileName": "yeni_dosya_adi.txt"
}

### Bulk File Upload Test
POST {{baseUrl}}/api/v1/general/files/bulk-upload
Authorization: Bearer {{token}}
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="FolderId"

{{folderId}}
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="Files"; filename="test1.txt"
Content-Type: text/plain

Test dosya içeriği 1
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="Files"; filename="test2.txt"
Content-Type: text/plain

Test dosya içeriği 2
------WebKitFormBoundary7MA4YWxkTrZu0gW--

### Bulk File Delete Test
DELETE {{baseUrl}}/api/v1/general/files/bulk-delete
Authorization: Bearer {{token}}
Content-Type: application/json

[
    "{{fileId}}",
    "00000000-0000-0000-0000-000000000001"
]

### Hata Durumu Testleri

### Olmayan Klasör Rename
PUT {{baseUrl}}/api/v1/general/folders/00000000-0000-0000-0000-000000000000/rename
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "Name": "Test Klasör"
}

### Olmayan Dosya Rename
PUT {{baseUrl}}/api/v1/general/files/rename
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "Id": "00000000-0000-0000-0000-000000000000",
    "FileName": "test.txt"
}

### Geçersiz Klasör ID ile Bulk Upload
POST {{baseUrl}}/api/v1/general/files/bulk-upload
Authorization: Bearer {{token}}
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="FolderId"

00000000-0000-0000-0000-000000000000
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="Files"; filename="test.txt"
Content-Type: text/plain

Test içerik
------WebKitFormBoundary7MA4YWxkTrZu0gW--

### Olmayan Dosyalar ile Bulk Delete
DELETE {{baseUrl}}/api/v1/general/files/bulk-delete
Authorization: Bearer {{token}}
Content-Type: application/json

[
    "00000000-0000-0000-0000-000000000000",
    "00000000-0000-0000-0000-000000000001"
]

### Bulk Download Files Test
POST {{baseUrl}}/api/v1/general/files/bulk-download
Authorization: Bearer {{token}}
Content-Type: application/json

[
    "{{fileId}}",
    "00000000-0000-0000-0000-000000000001"
]
