using System.Text.Json;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;
using Shared.Infrastructure.Localization;
using Users.Application.Abstractions;

namespace Users.Application.CustomerListFilters.GetCustomerFilters;

public class GetCustomerListFiltersQueryHandler : IRequestHandler<GetCustomerListFiltersQuery, Result<List<GetCustomerListFilterDto>>>
{
    private readonly IUserDbContext _context;
    private readonly ILocalizer _localizer;
    private readonly IWorkContext _workContext;

    public GetCustomerListFiltersQueryHandler(IUserDbContext context, ILocalizer localizer, IWorkContext workContext)
    {
        _context = context;
        _localizer = localizer;
        _workContext = workContext;
    }

    public async Task<Result<List<GetCustomerListFilterDto>>> Handle(GetCustomerListFiltersQuery request, CancellationToken cancellationToken)
    {
        var userId = _workContext.UserId;
        if (request.UserId != userId)
        {
            return Result.Failure<List<GetCustomerListFilterDto>>(_localizer.Get("UnauthorizedAccess"));
        }


        var filters = await _context.CustomerListFilters
            .Where(x => x.UserId == request.UserId)
            .OrderByDescending(x => x.CreatedAt)
            .ToListAsync(cancellationToken);

        if (filters.Count == 0)
        {
            return Result.Success(new List<GetCustomerListFilterDto>());
        }

        var mapped = filters.Select(x => new GetCustomerListFilterDto
        {
            Id = x.Id,
            Name = x.Name,
            Filters = JsonSerializer.Deserialize<Dictionary<string, object>>(x.FilterJson ?? "{}"),
            CreatedAt = x.CreatedAt
        }).ToList();

        return Result.Success(mapped);
    }
}
