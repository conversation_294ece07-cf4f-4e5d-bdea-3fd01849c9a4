namespace General.Infrastructure.Configuration;

public class FileUploadSettings
{
    public const string SectionName = "FileUpload";

    public long MaxFileSizeBytes { get; set; } = 100 * 1024 * 1024; // 100MB
    public int MaxBulkUploadCount { get; set; } = 50;
    public List<string> AllowedExtensions { get; set; } =
    [
        // Images
        ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp", ".svg",
        // Documents
        ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".txt", ".rtf",
        // Archives
        ".zip", ".rar", ".7z", ".tar", ".gz",
        // Audio/Video
        ".mp3", ".wav", ".mp4", ".avi", ".mov", ".wmv", ".flv",
        // Other
        ".csv", ".xml", ".json"
    ];
    public List<string> AllowedMimeTypes { get; set; } =
    [
        // Images
        "image/jpeg", "image/png", "image/gif", "image/bmp", "image/webp", "image/svg+xml",
        // Documents
        "application/pdf",
        "application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "application/vnd.ms-excel", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        "application/vnd.ms-powerpoint", "application/vnd.openxmlformats-officedocument.presentationml.presentation",
        "text/plain", "application/rtf",
        // Archives
        "application/zip", "application/x-rar-compressed", "application/x-7z-compressed",
        "application/x-tar", "application/gzip",
        // Audio/Video
        "audio/mpeg", "audio/wav", "video/mp4", "video/x-msvideo", "video/quicktime",
        "video/x-ms-wmv", "video/x-flv",
        // Other
        "text/csv", "application/xml", "application/json"
    ];
}
