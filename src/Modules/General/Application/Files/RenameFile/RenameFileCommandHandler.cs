using General.Domain;
using General.Infrastructure.Data;
using General.Infrastructure.Services;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace General.Application.Files.RenameFile;

public class RenameFileCommandHandler(
    GeneralDbContext dbContext,
    IStorageService storageService
) : IRequestHandler<RenameFileCommand, Result>
{
    private readonly GeneralDbContext _dbContext = dbContext;
    private readonly IStorageService _storageService = storageService;

    public async Task<Result> Handle(RenameFileCommand request, CancellationToken cancellationToken)
    {
        var file = await _dbContext.File
            .Include(f => f.Folder)
            .FirstOrDefaultAsync(f => f.Id == request.Id, cancellationToken);

        if (file == null)
        {
            return Result.Failure(GeneralErrors.FileNotFound(request.Id));
        }

        // Aynı klasörde aynı isimde başka dosya var mı kontrol et
        var existingFile = await _dbContext.File
            .FirstOrDefaultAsync(f =>
                f.FolderId == file.FolderId &&
                f.FileName == request.FileName &&
                f.Id != request.Id,
                cancellationToken);

        if (existingFile != null)
        {
            return Result.Failure(GeneralErrors.FileNameExists(request.FileName, file.FolderId));
        }

        try
        {
            // Fiziksel dosyayı yeniden adlandır
            var oldStoragePath = file.StoragePath;
            var newStoragePath = Path.Combine(file.Folder.Path, request.FileName);

            var isRenamed = await _storageService.RenameFileAsync(oldStoragePath, newStoragePath, cancellationToken);

            if (!isRenamed)
            {
                return Result.Failure(GeneralErrors.FileRenameFailed("Fiziksel dosya yeniden adlandırılamadı."));
            }

            // Veritabanı kaydını güncelle
            file.Rename(request.FileName);
            file.MoveToFolder(file.FolderId, newStoragePath);

            await _dbContext.SaveChangesAsync(cancellationToken);

            return Result.Success();
        }
        catch (Exception ex)
        {
            return Result.Failure(GeneralErrors.FileRenameFailed(ex.Message));
        }
    }
}
