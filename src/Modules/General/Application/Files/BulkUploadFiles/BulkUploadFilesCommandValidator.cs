using FluentValidation;
using General.Infrastructure.Services;

namespace General.Application.Files.BulkUploadFiles;

internal class BulkUploadFilesCommandValidator : AbstractValidator<BulkUploadFilesCommand>
{
    public BulkUploadFilesCommandValidator(IFileValidationService fileValidationService)
    {
        RuleFor(x => x.FolderId)
            .NotEmpty()
            .WithMessage("Klasör ID'si boş olamaz.");

        RuleFor(x => x.Files)
            .NotEmpty()
            .WithMessage("En az bir dosya seçilmelidir.")
            .Must(files => fileValidationService.IsValidBulkUploadCount(files.Count))
            .WithMessage("Toplu yükleme dosya sayısı limiti aşıldı.")
            .Must(files => !fileValidationService.GetValidationErrorMessages(files).Any())
            .WithMessage(x => string.Join("; ", fileValidationService.GetValidationErrorMessages(x.Files)));
    }
}
