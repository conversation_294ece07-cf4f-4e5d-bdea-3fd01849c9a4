using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using Shared.Application;
using Shared.Endpoints;

namespace General.Application.Files.BulkUploadFiles;

public class BulkUploadFilesEndpoint : IEndpoint
{
    public record BulkUploadFilesRequest(IFormFileCollection Files, Guid FolderId)
    {
        public Dictionary<string, object>? Metadata { get; set; }
    };

    public void MapEndpoint(IEndpointRouteBuilder endpoints)
    {
        endpoints.MapPost("/api/v1/general/files/bulk-upload", async (
            [FromForm] BulkUploadFilesRequest request,
            ISender sender) =>
        {
            var command = new BulkUploadFilesCommand(request.Files, request.FolderId, request.Metadata);
            var result = await sender.Send(command);
            return result.Match(Results.Ok, CustomResults.Problem);
        })
        .DisableAntiforgery()
        .WithTags("General.Files")
        .WithGroupName("apiv1")
        .RequireAuthorization("General.Files")
        .Produces<Result<List<Guid>>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest);
    }
}
