using General.Domain;
using General.Infrastructure.Data;
using General.Infrastructure.Services;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace General.Application.Files.BulkUploadFiles;

public class BulkUploadFilesCommandHandler : IRequestHandler<BulkUploadFilesCommand, Result<List<Guid>>>
{
    private readonly GeneralDbContext _dbContext;
    private readonly IStorageService _storageService;

    public BulkUploadFilesCommandHandler(GeneralDbContext dbContext, IStorageService storageService)
    {
        _dbContext = dbContext;
        _storageService = storageService;
    }

    public async Task<Result<List<Guid>>> Handle(BulkUploadFilesCommand request, CancellationToken cancellationToken)
    {
        // Klasörün var olduğunu kontrol et
        var folder = await _dbContext.Folder
            .FirstOrDefaultAsync(f => f.Id == request.FolderId, cancellationToken);

        if (folder == null)
        {
            return Result.Failure<List<Guid>>(GeneralErrors.FolderNotFound(request.FolderId));
        }

        var uploadedFileIds = new List<Guid>();
        var failedFiles = new List<string>();

        foreach (var file in request.Files)
        {
            try
            {
                // Dosyayı kaydet
                var (fileName, storagePath) = await _storageService.SaveFileAsync(
                    file,
                    folder.Path,
                    cancellationToken);

                // Dosya bilgilerini oluştur
                var fileEntity = Domain.File.Create(
                    fileName,
                    file.FileName,
                    Path.GetExtension(file.FileName),
                    file.ContentType,
                    file.Length,
                    folder.Id,
                    storagePath);

                // Metadata varsa ekle
                if (request.Metadata != null)
                {
                    fileEntity.UpdateMetadata(request.Metadata);
                }

                // Dosyayı veritabanına kaydet
                _dbContext.File.Add(fileEntity);
                await _dbContext.SaveChangesAsync(cancellationToken);

                // Event'i yayınla
                fileEntity.Raise(new FileUploadedEvent(fileEntity.Id));

                uploadedFileIds.Add(fileEntity.Id);
            }
            catch (Exception ex)
            {
                failedFiles.Add($"{file.FileName}: {ex.Message}");
            }
        }

        await _dbContext.SaveChangesAsync(cancellationToken);

        if (failedFiles.Any() && !uploadedFileIds.Any())
        {
            return Result.Failure<List<Guid>>(GeneralErrors.FileUploadFailed($"Tüm dosyalar başarısız: {string.Join(", ", failedFiles)}"));
        }

        if (failedFiles.Any())
        {
            return Result.Failure<List<Guid>>(GeneralErrors.FileUploadFailed($"Bazı dosyalar başarısız: {string.Join(", ", failedFiles)}"));
        }

        return Result.Success(uploadedFileIds);
    }
}
