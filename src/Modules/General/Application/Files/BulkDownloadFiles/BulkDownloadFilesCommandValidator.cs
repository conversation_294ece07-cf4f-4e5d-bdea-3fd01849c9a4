using FluentValidation;

namespace General.Application.Files.BulkDownloadFiles;

internal class BulkDownloadFilesCommandValidator : AbstractValidator<BulkDownloadFilesCommand>
{
    public BulkDownloadFilesCommandValidator()
    {
        RuleFor(x => x.FileIds)
            .NotEmpty()
            .WithMessage("En az bir dosya ID'si belirtilmelidir.")
            .Must(ids => ids.Length <= 100)
            .WithMessage("Aynı anda en fazla 100 dosya indirilebilir.");

        RuleForEach(x => x.FileIds)
            .NotEmpty()
            .WithMessage("Dosya ID'si boş olamaz.");
    }
}
