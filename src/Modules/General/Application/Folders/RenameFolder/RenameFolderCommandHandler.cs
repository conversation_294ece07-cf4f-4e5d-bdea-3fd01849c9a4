using General.Domain;
using General.Infrastructure.Data;
using General.Infrastructure.Services;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace General.Application.Folders.RenameFolder;

public class RenameFolderCommandHandler(
    GeneralDbContext dbContext,
    IStorageService storageService
) : IRequestHandler<RenameFolderCommand, Result>
{
    private readonly GeneralDbContext _dbContext = dbContext;
    private readonly IStorageService _storageService = storageService;

    public async Task<Result> Handle(RenameFolderCommand request, CancellationToken cancellationToken)
    {
        var folder = await _dbContext.Folder
            .FirstOrDefaultAsync(f => f.Id == request.Id, cancellationToken);

        if (folder == null)
        {
            return Result.Failure(GeneralErrors.FolderNotFound(request.Id));
        }

        // Aynı parent klasör altında aynı isimde başka klasör var mı kontrol et
        var existingFolder = await _dbContext.Folder
            .FirstOrDefaultAsync(f =>
                f.ParentFolderId == folder.ParentFolderId &&
                f.Name == request.Name &&
                f.Id != request.Id,
                cancellationToken);

        if (existingFolder != null)
        {
            return Result.Failure(GeneralErrors.FolderNameExists(request.Name, folder.ParentFolderId));
        }

        try
        {
            // Yeni klasör path'ini oluştur
            string newFolderPath;
            if (folder.ParentFolderId.HasValue)
            {
                var parentFolder = await _dbContext.Folder
                    .FirstOrDefaultAsync(f => f.Id == folder.ParentFolderId, cancellationToken);
                newFolderPath = Path.Combine(parentFolder!.Path, request.Name);
            }
            else
            {
                newFolderPath = request.Name;
            }

            // Fiziksel klasörü yeniden adlandır
            var isRenamed = await _storageService.MoveFolderAsync(folder.Path, newFolderPath, cancellationToken);

            if (!isRenamed)
            {
                return Result.Failure(GeneralErrors.FolderRenameFailed("Fiziksel klasör yeniden adlandırılamadı."));
            }

            // Veritabanı kaydını güncelle
            folder.Update(request.Name);
            folder.UpdatePath(newFolderPath);

            await _dbContext.SaveChangesAsync(cancellationToken);

            return Result.Success();
        }
        catch (Exception ex)
        {
            return Result.Failure(GeneralErrors.FolderRenameFailed(ex.Message));
        }
    }
}
