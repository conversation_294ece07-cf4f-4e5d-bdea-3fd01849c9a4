using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Requests.Domain;
using Shared.Application;
using Shared.Endpoints;

namespace Requests.Application.Nodes.ListNodes;

internal sealed class ListNodesEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapGet("/api/v1/requests/nodes", async (
            IMediator mediator,
            CancellationToken cancellationToken,
            Guid? flowId = null,
            string? type = null,
            string? searchTerm = null,
            int pageNumber = 1,
            int pageSize = 10) =>
        {
            NodeType? nodeType = null;
            if (!string.IsNullOrEmpty(type) && Enum.TryParse<NodeType>(type, true, out var parsedType))
            {
                nodeType = parsedType;
            }
            var query = new ListNodesQuery(flowId, nodeType, searchTerm, pageNumber, pageSize);
            var result = await mediator.Send(query, cancellationToken);
            return result.Match(Results.Ok, CustomResults.Problem);
        })
        .WithTags("Requests.Nodes")
        .WithGroupName("apiv1")
        .Produces<PagedResult<NodeDto>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .RequireAuthorization("Requests.Nodes");
    }
}
