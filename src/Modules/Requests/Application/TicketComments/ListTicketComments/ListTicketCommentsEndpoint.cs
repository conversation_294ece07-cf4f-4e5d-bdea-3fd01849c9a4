using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Application;
using Shared.Endpoints;

namespace Requests.Application.TicketComments.ListTicketComments;

internal sealed class ListTicketCommentsEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapGet("/api/v1/requests/tickets/comments", async (
            [AsParameters] ListTicketCommentsQuery query,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var result = await mediator.Send(query, cancellationToken);
            return result.Match(Results.Ok, CustomResults.Problem);
        })
        .WithTags("Requests.TicketComments")
        .WithGroupName("apiv1")
        .Produces<PagedResult<TicketCommentDto>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .RequireAuthorization("Requests.TicketComments");
    }
}
