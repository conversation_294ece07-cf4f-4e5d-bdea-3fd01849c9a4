using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Application;
using Shared.Endpoints;

namespace Requests.Application.Rules.CreateRule;

public class CreateRuleEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPost("/api/v1/requests/transitions/{transitionId}/rules", async (
            Guid transitionId,
            CreateRuleRequest request,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var command = new CreateRuleCommand(
                transitionId,
                request.Name,
                request.Description,
                request.RuleType,
                request.Order,
                request.IsActive,
                request.Configuration
            );

            var result = await mediator.Send(command, cancellationToken);
            return result.Match(
                id => Results.Created($"/api/v1/requests/rules/{id}", id),
                CustomResults.Problem);
        })
        .WithTags("Requests.Rules")
        .WithGroupName("apiv1")
        .Produces<Result<Guid>>(StatusCodes.Status201Created)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .RequireAuthorization("Requests.Rules");
    }
}

public record CreateRuleRequest(
    string Name,
    string Description,
    Domain.Enums.RuleType RuleType,
    int Order,
    bool IsActive,
    string Configuration
);
