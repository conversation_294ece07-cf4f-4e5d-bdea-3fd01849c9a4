using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Application;
using Shared.Endpoints;

namespace Requests.Application.Rules.GetRuleTypes;

internal sealed class GetRuleTypesEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapGet("/api/v1/requests/rule-types", async (
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var query = new GetRuleTypesQuery();
            var result = await mediator.Send(query, cancellationToken);
            return result.Match(Results.Ok, CustomResults.Problem);
        })
        .WithTags("Requests.RuleTypes")
        .WithGroupName("apiv1")
        .Produces<Result<List<RuleTypeDto>>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .RequireAuthorization("Requests.Rules");
    }
}
