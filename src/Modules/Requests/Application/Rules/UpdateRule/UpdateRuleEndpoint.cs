using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Requests.Domain.Enums;
using Shared.Application;
using Shared.Endpoints;

namespace Requests.Application.Rules.UpdateRule;

internal sealed class UpdateRuleEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPut("/api/v1/requests/rules/{id}", async (
            Guid id,
            UpdateRuleRequest request,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var command = new UpdateRuleCommand(
                id,
                request.Name,
                request.Description,
                request.RuleType,
                request.Order,
                request.IsActive,
                request.Configuration
            );
            var result = await mediator.Send(command, cancellationToken);
            return result.Match(Results.NoContent, CustomResults.Problem);
        })
        .WithTags("Requests.Rules")
        .WithGroupName("apiv1")
        .Produces<Result>(StatusCodes.Status204NoContent)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .RequireAuthorization("Requests.Rules");
    }
}

public record UpdateRuleRequest(
    string Name,
    string Description,
    RuleType RuleType,
    int Order,
    bool IsActive,
    string Configuration
);
