using Microsoft.Extensions.Logging;
using Requests.Application.Rules.Engine.Configurations;
using Requests.Domain;
using Requests.Domain.Enums;

namespace Requests.Application.Rules.Engine.Handlers;

public class NotificationRuleHandler(
    ILogger<NotificationRuleHandler> logger) : BaseRuleHandler
{
    public override RuleType RuleType => RuleType.Notification;

    public static readonly string FormConfigurationJson = """
    {
      "title": "Bildirim Kuralı",
      "description": "Geçiş sırasında otomatik bildirim gönderir",
      "fields": [
        {
          "name": "Type",
          "label": "Bildirim Tipi",
          "type": "Dropdown",
          "multiple": false,
          "required": true,
          "description": "Bildirim gönderim yöntemi",
          "options": {
            "items": [
              {
                "value": 1,
                "label": "E-posta"
              },
              {
                "value": 2,
                "label": "SMS"
              },
              {
                "value": 3,
                "label": "Uygulama İçi"
              },
              {
                "value": 4,
                "label": "Push Bildirimi"
              }
            ]
          }
        },
        {
          "name": "Template",
          "label": "Şablon",
          "type": "TextBox",
          "required": true
        },
        {
          "name": "Subject",
          "label": "Konu",
          "type": "TextBox",
          "required": true
        },
        {
          "name": "Users",
          "label": "Kullanıcılar",
          "type": "Dropdown",
          "multiple": false,
          "required": false,
          "options": {
            "dataSource": "users"
          }
        }
      ]
    }
    """;

    public override async Task<RuleResult> ExecuteAsync(
        TransitionRule rule,
        TransitionContext context,
        CancellationToken cancellationToken)
    {
        try
        {
            var notification = GetConfiguration<NotificationConfiguration>(rule);
            var result = RuleResult.Success();

            // // Condition kontrolü
            // if (!string.IsNullOrEmpty(notification.Condition))
            // {
            //     var conditionMet = await EvaluateConditionAsync(notification.Condition, context);
            //     if (!conditionMet)
            //     {
            //         logger.LogInformation("Notification condition not met for rule {RuleId}", rule.Id);
            //     }
            // }

            var recipients = await GetRecipientsAsync(notification.Users, context);
            if (!recipients.Any())
            {
                logger.LogWarning("No recipients found for notification in rule {RuleId}", rule.Id);
            }

            var notificationRequest = new NotificationRequest
            {
                Type = notification.Type.ToString(),
                Recipients = recipients,
                Template = notification.Template,
                Subject = ProcessTemplate(notification.Subject, context),
                Data = CreateNotificationData(context)
            };

            result.PendingNotifications.Add(notificationRequest);

            logger.LogInformation("Notification queued for {RecipientCount} recipients from rule {RuleId}",
                recipients.Count, rule.Id);

            return result;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Notification rule execution failed for rule {RuleId}", rule.Id);
            return RuleResult.Failure("Bildirim gönderme sırasında hata oluştu");
        }
    }

    // private async Task<bool> EvaluateConditionAsync(string condition, TransitionContext context)
    // {
    //     await Task.CompletedTask;

    //     // Basit condition evaluation
    //     // Örnek: "Priority == 'High'"
    //     try
    //     {
    //         // TODO: Daha gelişmiş expression evaluation implementasyonu
    //         if (condition.Contains("Priority == 'High'"))
    //         {
    //             var priority = context.GetTicketField<string>("Priority");
    //             return priority == "High";
    //         }

    //         // Şimdilik true dön
    //         return true;
    //     }
    //     catch (Exception ex)
    //     {
    //         logger.LogError(ex, "Condition evaluation failed: {Condition}", condition);
    //         return false;
    //     }
    // }

    private async Task<List<Guid>> GetRecipientsAsync(List<string>? users, TransitionContext context)
    {
        await Task.CompletedTask;
        var result = new List<Guid>();
        if (users?.Any() == true)
        {
            foreach (var userIdStr in users)
            {
                if (Guid.TryParse(userIdStr, out var userId))
                {
                    result.Add(userId);
                }
            }
        }
        // switch (recipients.Type)
        // {
        //     case RecipientType.Field:
        //         if (!string.IsNullOrEmpty(recipients.FieldName))
        //         {
        //             var fieldValue = context.GetTicketField<Guid?>(recipients.FieldName);
        //             if (fieldValue.HasValue)
        //             {
        //                 result.Add(fieldValue.Value);
        //             }
        //         }
        //         break;

        //     case RecipientType.Role:
        //         if (recipients.Roles?.Any() == true)
        //         {
        //             // TODO: Role'e sahip kullanıcıları getir
        //             logger.LogInformation("Getting users with roles: {Roles}", string.Join(", ", recipients.Roles));
        //         }
        //         break;

        //     case RecipientType.User:
        //         if (recipients.Users?.Any() == true)
        //         {
        //             foreach (var userIdStr in recipients.Users)
        //             {
        //                 if (Guid.TryParse(userIdStr, out var userId))
        //                 {
        //                     result.Add(userId);
        //                 }
        //             }
        //         }
        //         break;

        //     case RecipientType.CurrentUser:
        //         result.Add(context.UserId);
        //         break;
        // }

        return result;
    }

    private string ProcessTemplate(string template, TransitionContext context)
    {
        var result = template
            .Replace("{{TicketTitle}}", context.GetTicketField<string>("Title") ?? "")
            .Replace("{{TicketId}}", context.TicketId.ToString())
            .Replace("{{FromNodeName}}", context.FromNode.Name)
            .Replace("{{ToNodeName}}", context.ToNode.Name)
            .Replace("{{UserName}}", context.GetUserField<string>("Name") ?? "")
            .Replace("{{TransitionTime}}", context.TransitionTime.ToString("dd.MM.yyyy HH:mm"));

        return result;
    }

    private Dictionary<string, object> CreateNotificationData(TransitionContext context)
    {
        return new Dictionary<string, object>
        {
            ["TicketId"] = context.TicketId,
            ["UserId"] = context.UserId,
            ["FromNodeName"] = context.FromNode.Name,
            ["ToNodeName"] = context.ToNode.Name,
            ["TransitionTime"] = context.TransitionTime,
            ["TicketData"] = context.TicketData,
            ["UserData"] = context.UserData
        };
    }
}
