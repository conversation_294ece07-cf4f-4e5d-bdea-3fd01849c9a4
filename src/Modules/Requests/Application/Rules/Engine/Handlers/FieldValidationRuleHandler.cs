using System.Text.RegularExpressions;
using Microsoft.Extensions.Logging;
using Requests.Application.Rules.Engine.Configurations;
using Requests.Domain;
using Requests.Domain.Enums;

namespace Requests.Application.Rules.Engine.Handlers;

public class FieldValidationRuleHandler(
    ILogger<FieldValidationRuleHandler> logger) : BaseRuleHandler
{
    public override RuleType RuleType => RuleType.FieldValidation;

    public static readonly string FormConfigurationJson = """
    {
      "title": "Alan <PERSON>",
      "description": "Geçiş öncesi ticket alanlarını doğrular",
      "fields": [
        {
          "name": "FieldName",
          "label": "Alan Ad<PERSON>",
          "type": "Dropdown",
          "multiple": false,
          "required": true,
          "options": {
            "items": [
              {
                "value": "Title",
                "label": "Başlık"
              },
              {
                "value": "Description",
                "label": "Açıklama"
              },
              {
                "value": "Priority",
                "label": "Öncelik"
              },
              {
                "value": "Status",
                "label": "Durum"
              },
              {
                "value": "AssignedUserId",
                "label": "<PERSON>ana<PERSON>"
              },
              {
                "value": "AssignedDepartmentId",
                "label": "Atanan <PERSON>partman"
              },
              {
                "value": "DueDate",
                "label": "Bitiş Tarihi"
              },
              {
                "value": "EstimatedHours",
                "label": "Tahmini Saat"
              },
              {
                "value": "ActualHours",
                "label": "Gerçek Saat"
              },
              {
                "value": "Tags",
                "label": "Etiketler"
              },
              {
                "value": "CustomField1",
                "label": "Özel Alan 1"
              },
              {
                "value": "CustomField2",
                "label": "Özel Alan 2"
              },
              {
                "value": "CustomField3",
                "label": "Özel Alan 3"
              }
            ]
          }
        },
        {
          "name": "ValidationType",
          "label": "Doğrulama Tipi",
          "type": "Dropdown",
          "multiple": false,
          "required": true,
          "options": {
            "items": [
              {
                "value": 1,
                "label": "Zorunlu Alan"
              },
              {
                "value": 2,
                "label": "Minimum Uzunluk"
              },
              {
                "value": 3,
                "label": "Maksimum Uzunluk"
              },
              {
                "value": 4,
                "label": "Regex Deseni"
              },
              {
                "value": 5,
                "label": "Eşit Olmalı"
              },
              {
                "value": 6,
                "label": "Eşit Olmamalı"
              },
              {
                "value": 7,
                "label": "Büyük Olmalı"
              },
              {
                "value": 8,
                "label": "Küçük Olmalı"
              }
            ]
          }
        },
        {
          "name": "IsRequired",
          "label": "Zorunlu",
          "type": "Checkbox",
          "showTopField": "ValidationType",
          "showTopValue": [1],
          "required": false
        },
        {
          "name": "MinLength",
          "label": "Minimum Uzunluk",
          "type": "Number",
          "showTopField": "ValidationType",
          "showTopValue": [2],
          "required": false,
          "min": 0
        },
        {
          "name": "MaxLength",
          "label": "Maksimum Uzunluk",
          "type": "Number",
          "showTopField": "ValidationType",
          "showTopValue": [3],
          "required": false,
          "min": 1
        },
        {
          "name": "Pattern",
          "label": "Regex Deseni",
          "type": "TextBox",
          "showTopField": "ValidationType",
          "showTopValue": [4],
          "required": false,
          "placeholder": "Örnek: ^[A-Za-z0-9]+$"
        },
        {
          "name": "Value",
          "label": "Karşılaştırma Değeri",
          "type": "TextBox",
          "showTopField": "ValidationType",
          "showTopValue": [5,6,7,8],
          "required": false
        },
        {
          "name": "ErrorMessage",
          "label": "Hata Mesajı",
          "type": "TextBox",
          "required": false
        }
      ]
    }
    """;

    public override async Task<RuleResult> ExecuteAsync(
        TransitionRule rule,
        TransitionContext context,
        CancellationToken cancellationToken)
    {
        try
        {
            var validationRule = GetConfiguration<FieldValidationConfiguration>(rule);
            var errors = new List<string>();

            var fieldValue = context.GetTicketField<object>(validationRule.FieldName);
            var validationResult = await ValidateFieldAsync(validationRule, fieldValue);

            if (!validationResult.IsValid)
            {
                errors.Add(validationResult.ErrorMessage);
            }

            if (errors.Any())
            {
                return RuleResult.Failure(string.Join(", ", errors), isBlocking: true);
            }

            return RuleResult.Success();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Field validation rule execution failed for rule {RuleId}", rule.Id);
            return RuleResult.Failure("Alan doğrulama sırasında hata oluştu", isBlocking: true);
        }
    }

    private async Task<ValidationResult> ValidateFieldAsync(FieldValidationConfiguration rule, object? fieldValue)
    {
        await Task.CompletedTask;

        var stringValue = fieldValue?.ToString() ?? string.Empty;

        return rule.ValidationType switch
        {
            ValidationType.Required => ValidateRequired(rule, fieldValue),
            ValidationType.MinLength => ValidateMinLength(rule, stringValue),
            ValidationType.MaxLength => ValidateMaxLength(rule, stringValue),
            ValidationType.Pattern => ValidatePattern(rule, stringValue),
            ValidationType.Equals => ValidateEquals(rule, stringValue),
            ValidationType.NotEquals => ValidateNotEquals(rule, stringValue),
            _ => new ValidationResult { IsValid = true }
        };
    }

    private ValidationResult ValidateRequired(FieldValidationConfiguration rule, object? fieldValue)
    {
        if (rule.IsRequired && (fieldValue == null || string.IsNullOrWhiteSpace(fieldValue.ToString())))
        {
            return new ValidationResult
            {
                IsValid = false,
                ErrorMessage = !string.IsNullOrEmpty(rule.ErrorMessage)
                    ? rule.ErrorMessage
                    : $"{rule.FieldName} alanı zorunludur"
            };
        }

        return new ValidationResult { IsValid = true };
    }

    private ValidationResult ValidateMinLength(FieldValidationConfiguration rule, string value)
    {
        if (rule.MinLength.HasValue && value.Length < rule.MinLength.Value)
        {
            return new ValidationResult
            {
                IsValid = false,
                ErrorMessage = !string.IsNullOrEmpty(rule.ErrorMessage)
                    ? rule.ErrorMessage
                    : $"{rule.FieldName} alanı en az {rule.MinLength} karakter olmalıdır"
            };
        }

        return new ValidationResult { IsValid = true };
    }

    private ValidationResult ValidateMaxLength(FieldValidationConfiguration rule, string value)
    {
        if (rule.MaxLength.HasValue && value.Length > rule.MaxLength.Value)
        {
            return new ValidationResult
            {
                IsValid = false,
                ErrorMessage = !string.IsNullOrEmpty(rule.ErrorMessage)
                    ? rule.ErrorMessage
                    : $"{rule.FieldName} alanı en fazla {rule.MaxLength} karakter olmalıdır"
            };
        }

        return new ValidationResult { IsValid = true };
    }

    private ValidationResult ValidatePattern(FieldValidationConfiguration rule, string value)
    {
        if (!string.IsNullOrEmpty(rule.Pattern) && !Regex.IsMatch(value, rule.Pattern))
        {
            return new ValidationResult
            {
                IsValid = false,
                ErrorMessage = !string.IsNullOrEmpty(rule.ErrorMessage)
                    ? rule.ErrorMessage
                    : $"{rule.FieldName} alanı geçerli formatta değil"
            };
        }

        return new ValidationResult { IsValid = true };
    }

    private ValidationResult ValidateEquals(FieldValidationConfiguration rule, string value)
    {
        if (!string.IsNullOrEmpty(rule.Value) && value != rule.Value)
        {
            return new ValidationResult
            {
                IsValid = false,
                ErrorMessage = !string.IsNullOrEmpty(rule.ErrorMessage)
                    ? rule.ErrorMessage
                    : $"{rule.FieldName} alanı '{rule.Value}' değerine eşit olmalıdır"
            };
        }

        return new ValidationResult { IsValid = true };
    }

    private ValidationResult ValidateNotEquals(FieldValidationConfiguration rule, string value)
    {
        if (!string.IsNullOrEmpty(rule.Value) && value == rule.Value)
        {
            return new ValidationResult
            {
                IsValid = false,
                ErrorMessage = !string.IsNullOrEmpty(rule.ErrorMessage)
                    ? rule.ErrorMessage
                    : $"{rule.FieldName} alanı '{rule.Value}' değerine eşit olmamalıdır"
            };
        }

        return new ValidationResult { IsValid = true };
    }

    private class ValidationResult
    {
        public bool IsValid { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
    }
}
