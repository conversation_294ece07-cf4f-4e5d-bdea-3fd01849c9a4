using MediatR;
using Microsoft.EntityFrameworkCore;
using Requests.Application.Abstractions;
using Shared.Application;

namespace Requests.Application.Rules.ListRules;

internal sealed class ListRulesQueryHandler(IRequestsDbContext context)
    : IRequestHandler<ListRulesQuery, PagedResult<RuleListItemDto>>
{
    public async Task<PagedResult<RuleListItemDto>> Handle(ListRulesQuery request, CancellationToken cancellationToken)
    {
        var query = context.TransitionRules.AsQueryable();

        if (request.TransitionId.HasValue)
        {
            query = query.Where(r => r.TransitionId == request.TransitionId.Value);
        }

        if (request.RuleType.HasValue)
        {
            query = query.Where(r => r.RuleType == request.RuleType.Value);
        }

        if (request.IsActive.HasValue)
        {
            query = query.Where(r => r.IsActive == request.IsActive.Value);
        }

        var filteredCount = await query.CountAsync(cancellationToken);
        var totalCount = await context.TransitionRules.CountAsync(cancellationToken);

        var rules = await query
            .OrderBy(r => r.Order)
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .Select(r => new RuleListItemDto(
                r.Id,
                r.TransitionId,
                r.Name,
                r.Description,
                r.RuleType,
                r.Order,
                r.IsActive,
                r.Configuration,
                r.InsertDate,
                r.UpdateDate
            ))
            .ToListAsync(cancellationToken);

        var pagedResult = new PagedResult<RuleListItemDto>(rules)
        {
            PageNumber = request.PageNumber,
            PageSize = request.PageSize,
            Count = totalCount,
            FilteredCount = filteredCount
        };

        return pagedResult;
    }
}
