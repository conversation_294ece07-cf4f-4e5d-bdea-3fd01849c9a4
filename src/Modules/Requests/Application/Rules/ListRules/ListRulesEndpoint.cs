using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Requests.Domain.Enums;
using Shared.Application;
using Shared.Endpoints;

namespace Requests.Application.Rules.ListRules;

internal sealed class ListRulesEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapGet("/api/v1/requests/transitions/{transitionId}/rules", async (
            Guid transitionId,
            RuleType? ruleType,
            bool? isActive,
            int pageNumber,
            int pageSize,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var query = new ListRulesQuery(
                transitionId,
                ruleType,
                isActive,
                pageNumber == 0 ? 1 : pageNumber,
                pageSize == 0 ? 10 : pageSize
            );
            var result = await mediator.Send(query, cancellationToken);
            return result.Match(Results.Ok, CustomResults.Problem);
        })
        .WithTags("Requests.Rules")
        .WithGroupName("apiv1")
        .Produces<PagedResult<RuleListItemDto>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .RequireAuthorization("Requests.Rules");

        app.MapGet("/api/v1/requests/rules", async (
            Guid? transitionId,
            RuleType? ruleType,
            bool? isActive,
            int pageNumber,
            int pageSize,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var query = new ListRulesQuery(
                transitionId,
                ruleType,
                isActive,
                pageNumber == 0 ? 1 : pageNumber,
                pageSize == 0 ? 10 : pageSize
            );
            var result = await mediator.Send(query, cancellationToken);
            return result.Match(Results.Ok, CustomResults.Problem);
        })
        .WithTags("Requests.Rules")
        .WithGroupName("apiv1")
        .Produces<PagedResult<RuleListItemDto>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .RequireAuthorization("Requests.Rules");
    }
}
