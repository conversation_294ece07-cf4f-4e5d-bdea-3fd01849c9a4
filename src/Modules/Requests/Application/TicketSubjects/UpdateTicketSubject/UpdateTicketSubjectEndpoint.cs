using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Application;
using Shared.Endpoints;

namespace Requests.Application.TicketSubjects.UpdateTicketSubject;

internal sealed class UpdateTicketSubjectEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPut("/api/v1/requests/ticket-subjects/{id}", async (
            Guid id,
            UpdateTicketSubjectCommand command,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            // Ensure the ID in the route matches the ID in the command
            if (id != command.Id)
            {
                command = command with { Id = id };
            }

            var result = await mediator.Send(command, cancellationToken);
            return result.Match(Results.NoContent, CustomResults.Problem);
        })
        .WithTags("Requests.TicketSubjects")
        .WithGroupName("apiv1")
        .Produces<Result>(StatusCodes.Status204NoContent)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .RequireAuthorization("Requests.TicketSubjects");
    }
}
