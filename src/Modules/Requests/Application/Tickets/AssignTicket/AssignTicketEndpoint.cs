using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Application;
using Shared.Endpoints;

namespace Requests.Application.Tickets.AssignTicket;

internal sealed class AssignTicketEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPost("/api/v1/requests/tickets/{ticketId}/assign", async (
            Guid ticketId,
            AssignTicketCommand command,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            // Ensure the ID in the route matches the ID in the command
            if (ticketId != command.TicketId)
            {
                command = command with { TicketId = ticketId };
            }

            var result = await mediator.Send(command, cancellationToken);
            return result.Match(Results.NoContent, CustomResults.Problem);
        })
        .WithTags("Requests.Tickets")
        .WithGroupName("apiv1")
        .Produces<Result>(StatusCodes.Status204NoContent)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status404NotFound)
        .RequireAuthorization("Requests.Tickets");
    }
}
