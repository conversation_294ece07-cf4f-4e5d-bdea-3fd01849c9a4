using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Application;
using Shared.Endpoints;

namespace Requests.Application.Transitions.ListTransitions;

internal sealed class ListTransitionsEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapGet("/api/v1/requests/transitions", async (
            IMediator mediator,
            CancellationToken cancellationToken,
            Guid? fromNodeId = null,
            Guid? toNodeId = null,
            string? searchTerm = null,
            int pageNumber = 1,
            int pageSize = 10) =>
        {
            var query = new ListTransitionsQuery(fromNodeId, toNodeId, searchTerm, pageNumber, pageSize);
            var result = await mediator.Send(query, cancellationToken);
            return result.Match(Results.Ok, CustomResults.Problem);
        })
        .WithTags("Requests.Transitions")
        .WithGroupName("apiv1")
        .Produces<PagedResult<TransitionDto>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .RequireAuthorization("Requests.Transitions");
    }
}
