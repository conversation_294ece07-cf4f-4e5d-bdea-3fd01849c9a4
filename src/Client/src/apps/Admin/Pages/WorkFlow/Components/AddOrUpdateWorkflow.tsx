import useMazakaForm from "@/hooks/useMazakaForm";
import { Col, Form, Row } from "antd";
import { useQueryClient } from "react-query";
import endPoints from "../EndPoints";
import { useTranslation } from "react-i18next";
import { FC, useEffect } from "react";
import { MazakaForm } from "@/apps/Common/MazakaForm";
import { MazakaButton } from "@/apps/Common/MazakaButton";
import { MazakaInput } from "@/apps/Common/MazakaInput";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import { useNavigate } from "react-router-dom";
import { MazakaTextArea } from "@/apps/Common/MazakaTextarea";
import {
  addNodeToWorkFlow,
  copyWorkFlow,
  createWorkFlow,
  updateWorkFlowWithPut,
} from "../Services";
import { commonRoutePrefix } from "@/routes/Prefix";

const AddOrUpdateWorkflowName: FC<{
  onFinish: () => void;
  selectedRecord?: any;
  mode: "copy" | "add" | "edit";
}> = ({ onFinish, selectedRecord, mode }) => {

  const [form] = Form.useForm();
  const navigate = useNavigate();
  const { formActions, mazakaForm } = useMazakaForm(form);

  const queryClient = useQueryClient();
  const { t } = useTranslation();

  useEffect(() => {
    if ((mode === "add" || mode === "edit") && selectedRecord) {
      form.setFieldsValue({
        Name:selectedRecord?.Name,
        Description:selectedRecord?.Description
      });
    }
  }, [selectedRecord]);

  const handleOnFinish = async () => {
    mazakaForm.setLoading();
    const formValues = form.getFieldsValue();

    try {
      if (mode === "copy") {
        let data = {
          Id: selectedRecord?.Id,
          NewName: formValues["NewName"],
          NewDescription: formValues["NewDescription"] || "",
        };
        await copyWorkFlow(data);
      } else {
        if (selectedRecord?.Id) {
          await updateWorkFlowWithPut({ ...selectedRecord, ...formValues });
        } else {
          formValues["Description"] = formValues["Description"] || "";
          const response = await createWorkFlow(formValues);
          const flowId = response?.Value;
          if (!flowId) {
            openNotificationWithIcon(
              "error",
              t("workFlow.errorCreateWorkFlow")
            );
            return false;
          }
          let nodes = [
            { Name: t("workFlow.start"), NodeType: 1, FlowId: flowId,PositionX: -60,PositionY:-645},
            { Name: t("workFlow.end"), NodeType: 3, FlowId: flowId,PositionX: -40,PositionY:-330 },
          ];
          await addNodeToWorkFlow(nodes[0]);
          await addNodeToWorkFlow(nodes[1]);
          if (response?.Value) {
            navigate(`${commonRoutePrefix}/edit-workflow/${response?.Value}`);
          }
        }
      }
      mazakaForm.setSuccess(2000, () => t("form.transactionSuccessful"));
      openNotificationWithIcon("success", t("form.transactionSuccessful"));
      form.resetFields();

      queryClient.resetQueries({
        queryKey: endPoints.getWorkFlowListFilter,
        exact: false,
      });
      onFinish();
    } catch (error) {
      showErrorCatching(error, mazakaForm, true, t);
    }
  };

  return (
    <>
      <Col xs={24}>
        <MazakaForm
          form={form}
          onFinish={handleOnFinish}
          submitButtonVisible={false}
        >
          <Row gutter={[0, 10]}>
            <MazakaInput
              label={
                mode === "add" || mode === "edit"
                  ? t("workFlow.name")
                  : t("workFlow.newName")
              }
              placeholder={
                mode === "add" || mode === "edit"
                  ? t("workFlow.name")
                  : t("workFlow.newName")
              }
              xs={24}
              name={mode === "add" || mode === "edit" ? "Name" : "NewName"}
              rules={[{ required: true, message: "" }]}
            />
            <MazakaTextArea
              label={
                mode === "add" || mode === "edit"
                  ? t("workFlow.description")
                  : t("workFlow.newDescription")
              }
              placeholder={
                mode === "add" || mode === "edit"
                  ? t("workFlow.description")
                  : t("workFlow.newDescription")
              }
              xs={24}
              name={
                mode === "add" || mode === "edit"
                  ? "Description"
                  : "NewDescription"
              }
            />
            <Col xs={24}>
              <MazakaButton
                htmlType="submit"
                processType={formActions.submitProcessType}
                status="save"
              >
                {t("workFlow.save")}
              </MazakaButton>
            </Col>
          </Row>
        </MazakaForm>
      </Col>
    </>
  );
};

export default AddOrUpdateWorkflowName;
