import {  ProductOutlined } from "@ant-design/icons";
import { Drawer, FloatButton, Tooltip } from "antd";
import { FC, useState } from "react";
import { useTranslation } from "react-i18next";
import AddOrUpdateNode from "./AddOrUpdateNode";

const AddOrUpdateNodeButton: FC<{
  selectedRecord?: any;
  tourRef:any
}> = ({ selectedRecord,tourRef }) => {
  const { t } = useTranslation();
  const [isShowDrawer, setIsShowDrawer] = useState(false);
  return (
    <>
    <Tooltip
    title={t("workFlow.addStep")}
    >
      <FloatButton
      ref={tourRef}
      type="primary"
        icon={<ProductOutlined />}
          className="!bg-[#0096d1]"
        onClick={() => {
          setIsShowDrawer(true);
        }}
        style={{
          top: "7%", // üst kenara hizala
          position: 'fixed', // sabitle
          left: '56%', // yatayda ortala
          transform: 'translateX(-53%)', // tam ortalamak için
          zIndex: 1000, // üstte kalması için isteğe bağlı
        }}
      />
      </Tooltip>

      <Drawer
       title={t("workFlow.addStep")}
        open={isShowDrawer}
        onClose={() => {
          setIsShowDrawer(false);
        }}
      >
        <AddOrUpdateNode
          selectedNode={selectedRecord}
          onFinish={() => {
            setIsShowDrawer(false);
          }}
        />
      </Drawer>
    </>
    
  );
};

export default AddOrUpdateNodeButton;
