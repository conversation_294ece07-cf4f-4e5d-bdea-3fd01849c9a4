import { MazakaButton } from "@/apps/Common/MazakaButton";
import { MazakaForm } from "@/apps/Common/MazakaForm";
import CreateFormItem from "@/apps/Form/CreateFormItem";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import useMazakaForm from "@/hooks/useMazakaForm";
import { Col, Form, Row } from "antd";
import { FC, Fragment } from "react";
import { useTranslation } from "react-i18next";
import { useQueryClient } from "react-query";
import endPoints from "../../../EndPoints";
import { createEdgeRule } from "../../../Services";
import { useSearchParams } from "react-router-dom";
import { useGetEdgeRules } from "../../../ServerSideStates";

const DynamicForm: FC<{ selectedRecord: any; onFinish: any }> = ({
  selectedRecord,
  onFinish,
}) => {
  const [form] = Form.useForm();
  const { formActions, mazakaForm } = useMazakaForm(form);

  const queryClient = useQueryClient();
  const { t } = useTranslation();
  const [searchParams] = useSearchParams();
 const rules = useGetEdgeRules({
  PageNumber: 1,
  PageSize: 100,
  transitionId: searchParams.get("edgeId")
 });
  

  const handleOnFinish = async () => {
    mazakaForm.setLoading();
    const formValues = form.getFieldsValue();
    let data = {
      Name: selectedRecord?.Name,
      Description: selectedRecord?.Description,
      RuleType: selectedRecord?.Id,
      Order: rules?.data?.Count? rules?.data?.Count+1:1,
      IsActive: true,
      Configuration: formValues ? JSON.stringify(formValues) : "",
    };

    try {
      await createEdgeRule(searchParams.get("edgeId") || "", data);
      mazakaForm.setSuccess(2000, () => t("form.transactionSuccessful"));
      openNotificationWithIcon("success", t("form.transactionSuccessful"));
      form.resetFields();

      queryClient.resetQueries({
        queryKey: endPoints.getEdgeRulesList,
        exact: false,
      });
      onFinish();

    } catch (error) {
      showErrorCatching(error, mazakaForm, true, t);
    }
  };

  return (
    <>
      <Col xs={24}>
        <MazakaForm
          form={form}
          onFinish={handleOnFinish}
          submitButtonVisible={false}
        >
          <Row gutter={[0, 10]}>
            {selectedRecord.FormConfigurationJson
              ? JSON.parse(selectedRecord.FormConfigurationJson).fields?.map(
                  (formItem: any, index: number) => {
                    console.log("form item", formItem);
                    return (
                      <Fragment key={index}>
                        <CreateFormItem item={formItem} xs={24} />
                      </Fragment>
                    );
                  }
                )
              : []}
            <Col xs={24}>
              <MazakaButton
                htmlType="submit"
                processType={formActions.submitProcessType}
                status="save"
              >
                {t("workFlow.save")}
              </MazakaButton>
            </Col>
          </Row>
        </MazakaForm>
      </Col>
    </>
  );
};

export default DynamicForm;
